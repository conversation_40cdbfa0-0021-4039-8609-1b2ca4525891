// pages/meihua/meihua.js - 梅花易数页面
const app = getApp();

Page({
  data: {
    currentTime: '',
    hexagram: null,
    analysis: '',
    isAnalyzing: false,
    question: '', // 要问的事
    selectedMethod: 'time', // 起卦方法
    methodList: [
      { id: 'time', name: '时间起卦', desc: '根据当前年月日时起卦' },
      { id: 'number', name: '物数起卦', desc: '根据看到的数字起卦' },
      { id: 'sound', name: '声音起卦', desc: '根据听到的声音次数起卦' },
      { id: 'word', name: '字数起卦', desc: '根据字的数量起卦' },
      { id: 'measure', name: '丈尺起卦', desc: '以丈数为上卦，尺数为下卦' },
      { id: 'person', name: '为人起卦', desc: '观人品、听语声、取诸身物' },
      { id: 'self', name: '自己起卦', desc: '年月日时或闻声观物' },
      { id: 'animal', name: '占动物', desc: '以物为上卦，方位为下卦' }
    ],
    inputNumber: '', // 物数或声音次数
    inputWords: '' // 字数起卦的文字
  },

  onLoad() {
    console.log('梅花易数页面加载');
    this.updateCurrentTime();
    
    // 每秒更新时间
    this.timeInterval = setInterval(() => {
      this.updateCurrentTime();
    }, 1000);
  },

  onUnload() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
  },

  // 更新当前时间
  updateCurrentTime() {
    const now = new Date();
    const timeStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日 ${now.getHours()}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    
    this.setData({
      currentTime: timeStr
    });
  },

  // 输入问题
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    });
  },

  // 选择起卦方法
  onMethodChange(e) {
    const index = e.detail.value;
    const method = this.data.methodList[index].id;
    this.setData({
      selectedMethod: method
    });
  },

  // 输入数字（物数或声音次数）
  onNumberInput(e) {
    this.setData({
      inputNumber: e.detail.value
    });
  },

  // 输入文字（字数起卦）
  onWordsInput(e) {
    this.setData({
      inputWords: e.detail.value
    });
  },

  // 开始起卦
  onStartDivination() {
    if (this.data.isAnalyzing) return;

    // 检查是否输入问题
    if (!this.data.question.trim()) {
      wx.showToast({
        title: '请输入要问的事',
        icon: 'none'
      });
      return;
    }

    // 根据不同方法检查输入
    const method = this.data.selectedMethod;
    if (['number', 'sound', 'measure'].includes(method)) {
      if (!this.data.inputNumber || this.data.inputNumber <= 0) {
        let title = '请输入有效数字';
        if (method === 'sound') title = '请输入声音次数';
        if (method === 'measure') title = '请输入丈尺数';
        wx.showToast({
          title: title,
          icon: 'none'
        });
        return;
      }
    } else if (['word', 'person', 'self', 'animal'].includes(method)) {
      if (!this.data.inputWords.trim()) {
        let title = '请输入要起卦的文字';
        if (method === 'person') title = '请输入观察到的特征';
        if (method === 'self') title = '请输入观察到的现象';
        if (method === 'animal') title = '请输入动物和方位';
        wx.showToast({
          title: title,
          icon: 'none'
        });
        return;
      }
    }



    this.setData({
      isAnalyzing: true,
      hexagram: null,
      analysis: ''
    });

    // 根据选择的方法起卦
    this.generateHexagramByMethod();
  },

  // 根据方法生成卦象
  generateHexagramByMethod() {
    const method = this.data.selectedMethod;
    let upperNum, lowerNum, changeNum;
    const now = new Date();
    const timeNum = now.getHours() + 1; // 时辰数（1-12）

    switch (method) {
      case 'time':
        // 年月日时起卦（按知识库中的方法）
        const year = now.getFullYear() % 12 || 12; // 地支年
        const month = now.getMonth() + 1;
        const day = now.getDate();
        const hour = Math.floor((now.getHours() + 1) / 2) + 1; // 时辰

        upperNum = (year + month + day) % 8;
        if (upperNum === 0) upperNum = 8;

        lowerNum = (year + month + day + hour) % 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = (year + month + day + hour) % 6;
        if (changeNum === 0) changeNum = 6;
        break;

      case 'number':
        // 物数占：以此数起作上卦，以时数配作下卦
        const num = parseInt(this.data.inputNumber);
        upperNum = num % 8;
        if (upperNum === 0) upperNum = 8;

        lowerNum = timeNum % 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = (num + timeNum) % 6;
        if (changeNum === 0) changeNum = 6;
        break;

      case 'sound':
        // 声音占：数得几数，起作上卦，加时数配作下卦
        const soundNum = parseInt(this.data.inputNumber);
        upperNum = soundNum % 8;
        if (upperNum === 0) upperNum = 8;

        lowerNum = timeNum % 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = (soundNum + timeNum) % 6;
        if (changeNum === 0) changeNum = 6;
        break;

      case 'word':
        // 字占：字数均匀则平分，不匀则少为上卦多为下卦
        const words = this.data.inputWords.trim();
        const wordCount = words.length;

        if (wordCount % 2 === 0) {
          // 字数均匀，平分
          upperNum = (wordCount / 2) % 8;
          lowerNum = (wordCount / 2) % 8;
        } else {
          // 字数不匀，少为上卦，多为下卦
          upperNum = Math.floor(wordCount / 2) % 8;
          lowerNum = Math.ceil(wordCount / 2) % 8;
        }

        if (upperNum === 0) upperNum = 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = wordCount % 6;
        if (changeNum === 0) changeNum = 6;
        break;

      case 'measure':
        // 丈尺占：以丈数为上卦，尺数为下卦
        const measureNum = parseInt(this.data.inputNumber);
        const zhangNum = Math.floor(measureNum / 10); // 丈数
        const chiNum = measureNum % 10; // 尺数

        upperNum = zhangNum % 8;
        if (upperNum === 0) upperNum = 8;

        lowerNum = chiNum % 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = (zhangNum + chiNum + timeNum) % 6;
        if (changeNum === 0) changeNum = 6;
        break;

      case 'person':
        // 为人占：听语声、观人品、取诸身、取诸物
        const personWords = this.data.inputWords.trim();
        const personCount = personWords.length;

        upperNum = personCount % 8;
        if (upperNum === 0) upperNum = 8;

        lowerNum = timeNum % 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = (personCount + timeNum) % 6;
        if (changeNum === 0) changeNum = 6;
        break;

      case 'self':
        // 自己占：年月日时或闻声音、观外物
        const selfWords = this.data.inputWords.trim();
        const selfCount = selfWords.length;
        const year2 = now.getFullYear() % 12 || 12;
        const month2 = now.getMonth() + 1;

        upperNum = (year2 + month2 + selfCount) % 8;
        if (upperNum === 0) upperNum = 8;

        lowerNum = (selfCount + timeNum) % 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = (year2 + month2 + selfCount + timeNum) % 6;
        if (changeNum === 0) changeNum = 6;
        break;

      case 'animal':
        // 占动物：以物为上卦，方位为下卦
        const animalWords = this.data.inputWords.trim();
        const animalCount = animalWords.length;

        upperNum = animalCount % 8;
        if (upperNum === 0) upperNum = 8;

        // 方位数（假设用时辰代表方位）
        lowerNum = timeNum % 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = (animalCount + timeNum) % 6;
        if (changeNum === 0) changeNum = 6;
        break;

      default:
        // 默认时间起卦
        const year3 = now.getFullYear() % 12 || 12;
        const month3 = now.getMonth() + 1;
        const day3 = now.getDate();
        const hour3 = Math.floor((now.getHours() + 1) / 2) + 1;

        upperNum = (year3 + month3 + day3) % 8;
        if (upperNum === 0) upperNum = 8;

        lowerNum = (year3 + month3 + day3 + hour3) % 8;
        if (lowerNum === 0) lowerNum = 8;

        changeNum = (year3 + month3 + day3 + hour3) % 6;
        if (changeNum === 0) changeNum = 6;
        break;
    }

    // 八卦对应（乾一、兑二、离三、震四、巽五、坎六、艮七、坤八）
    const trigrams = ['', '乾', '兑', '离', '震', '巽', '坎', '艮', '坤'];
    const trigramSymbols = ['', '☰', '☱', '☲', '☳', '☴', '☵', '☶', '☷'];

    const hexagram = {
      upper: {
        name: trigrams[upperNum],
        symbol: trigramSymbols[upperNum],
        number: upperNum
      },
      lower: {
        name: trigrams[lowerNum],
        symbol: trigramSymbols[lowerNum],
        number: lowerNum
      },
      change: changeNum,
      time: this.data.currentTime,
      method: method,
      question: this.data.question
    };

    setTimeout(() => {
      this.setData({
        hexagram: hexagram
      });

      // AI分析
      this.analyzeHexagram(hexagram);
    }, 1500);
  },

  // 分析卦象
  analyzeHexagram(hexagram) {
    // 这里应该调用DeepSeek API进行分析
    // 现在先用模拟数据
    setTimeout(() => {
      const methodNames = {
        'time': '时间起卦',
        'number': '物数起卦',
        'sound': '声音起卦',
        'word': '字数起卦'
      };

      const analysis = `【梅花易数占卜结果】

所问之事：${hexagram.question}
起卦方法：${methodNames[hexagram.method]}
起卦时间：${hexagram.time}

卦象组成：
上卦：${hexagram.upper.name}卦 ${hexagram.upper.symbol} (${hexagram.upper.number}数)
下卦：${hexagram.lower.name}卦 ${hexagram.lower.symbol} (${hexagram.lower.number}数)
动爻：第${hexagram.change}爻

卦象分析：
根据梅花易数体用生克理论，此卦象显示：

上卦${hexagram.upper.name}为外象，代表外在环境和他人因素
下卦${hexagram.lower.name}为内象，代表内在状态和自身条件
第${hexagram.change}爻发动，表示变化的关键所在

针对您所问"${hexagram.question}"：
此卦象暗示当前时机需要谨慎观察，宜静观其变。上下卦的组合显示了内外环境的相互作用，建议在处理此事时要平衡好各方面的关系。

占卜建议：
1. 顺应自然规律，不可强求
2. 注意内外环境的变化
3. 把握好变化的时机
4. 保持内心的平静和智慧

注：此为梅花易数传统占法，仅供参考，最终决策请结合实际情况。`;

      this.setData({
        analysis: analysis,
        isAnalyzing: false
      });



      wx.showToast({
        title: '占卜完成',
        icon: 'success'
      });
    }, 2000);
  },

  // 重新起卦
  onRestart() {
    this.setData({
      hexagram: null,
      analysis: '',
      isAnalyzing: false
    });
  }
});
