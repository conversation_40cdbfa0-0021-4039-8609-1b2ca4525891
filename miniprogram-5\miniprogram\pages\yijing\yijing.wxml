<!--pages/yijing/yijing.wxml - 周易卦象（六爻）页面模板-->
<view class="yijing-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">周易卦象</view>
    <view class="header-subtitle">朱熹原著 • 传统六爻</view>
  </view>

  <!-- 问事输入 -->
  <view class="question-section" wx:if="{{!hexagram && !isThrowingCoins && !isAnalyzing}}">
    <view class="section-title">要问的事</view>
    <ink-input
      label=""
      placeholder="请输入您要占卜的问题"
      value="{{question}}"
      maxlength="100"
      bind:input="onQuestionInput">
    </ink-input>
    <view class="input-tip">请诚心诚意地输入您要问的事情</view>
  </view>

  <!-- 起卦说明 -->
  <view class="method-section" wx:if="{{!hexagram && !isThrowingCoins && !isAnalyzing}}">
    <view class="section-title">起卦方法</view>
    <view class="method-desc">
      <text class="desc-text">采用传统三枚铜钱法起卦，共投掷六次，每次投掷三枚铜钱，根据正反面组合确定爻的阴阳和动静。</text>
    </view>
  </view>

  <!-- 起卦按钮 -->
  <view class="action-section" wx:if="{{!hexagram && !isThrowingCoins && !isAnalyzing}}">
    <ink-button
      text="开始六爻占卜"
      type="primary"
      size="large"
      bind:tap="onStartDivination">
    </ink-button>

  </view>

  <!-- 投币过程 -->
  <view class="throwing-section" wx:if="{{isThrowingCoins}}">
    <view class="throwing-header">
      <view class="throwing-title">正在投币起卦</view>
      <view class="throwing-progress">第{{currentThrow + 1}}爻 / 共6爻</view>
    </view>

    <view class="coin-animation">
      <ink-loading type="ripple" text="投掷铜钱中..."></ink-loading>
    </view>

    <!-- 已投币结果 -->
    <view class="coin-results" wx:if="{{coinResults.length > 0}}">
      <view class="results-title">投币结果</view>
      <view class="result-list">
        <view class="result-item" wx:for="{{coinResults}}" wx:key="throw">
          <view class="result-header">
            <text class="result-yao">第{{item.throw}}爻</text>
            <text class="result-type">{{item.yaoType}}{{item.isChanging ? ' (动)' : ''}}</text>
          </view>
          <view class="result-coins">
            <text class="coin" wx:for="{{item.coins}}" wx:for-item="coin" wx:key="*this">
              {{coin === 3 ? '正' : '反'}}
            </text>
            <text class="coin-total">= {{item.total}}</text>
          </view>
          <view class="result-symbol">{{item.yaoSymbol}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分析中状态 -->
  <view class="analyzing-section" wx:if="{{isAnalyzing}}">
    <ink-loading type="brush" text="AI智能解析中..."></ink-loading>
  </view>

  <!-- 卦象结果 -->
  <view class="hexagram-section" wx:if="{{hexagram}}">
    <view class="hexagram-header">
      <view class="hexagram-title">卦象结果</view>
      <view class="hexagram-question">所问：{{hexagram.question}}</view>
      <view class="hexagram-time">起卦时间：{{hexagram.time}}</view>
    </view>

    <!-- 六爻显示 -->
    <view class="hexagram-display">
      <view class="yaos-container">
        <view class="yao-item" wx:for="{{hexagram.yaos}}" wx:key="throw" wx:for-index="index">
          <view class="yao-position">第{{6-index}}爻</view>
          <view class="yao-symbol {{item.isChanging ? 'changing' : ''}}">{{item.yaoSymbol}}</view>
          <view class="yao-type">{{item.yaoType}}{{item.isChanging ? ' (动)' : ''}}</view>
        </view>
      </view>
    </view>

    <!-- 动爻提示 -->
    <view class="changing-yaos" wx:if="{{hexagram.changingYaos.length > 0}}">
      <text class="changing-text">动爻：第{{hexagram.changingYaos.join('、')}}爻</text>
    </view>
  </view>

  <!-- 分析结果 -->
  <view class="analysis-section" wx:if="{{analysis}}">
    <view class="analysis-header">
      <view class="analysis-title">AI智能解析</view>
    </view>

    <view class="analysis-content">
      <text class="analysis-text">{{analysis}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="bottom-actions" wx:if="{{hexagram}}">
    <ink-button
      text="重新占卜"
      type="secondary"
      size="large"
      bind:tap="onRestart">
    </ink-button>
  </view>
</view>