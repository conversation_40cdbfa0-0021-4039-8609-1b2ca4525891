<!--pages/bazi/bazi.wxml - 子平八字页面-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">子平八字</text>
    <text class="subtitle">朱熹原著·传统命理</text>
  </view>

  <!-- 输入区域 -->
  <view class="input-section" wx:if="{{!showResult}}">
    <!-- 问题输入 -->
    <view class="input-group">
      <text class="label">请输入您的问题：</text>
      <textarea
        class="question-input"
        placeholder="例如：我的财运如何？何时能升职？婚姻运势怎样？"
        value="{{question}}"
        bindinput="onQuestionInput"
        maxlength="200"
      ></textarea>
    </view>

    <!-- 出生信息 -->
    <view class="birth-info">
      <view class="info-row">
        <text class="label">出生日期：</text>
        <picker mode="date" value="{{birthDate}}" bindchange="onDateChange">
          <view class="picker">{{birthDate || '请选择'}}</view>
        </picker>
      </view>

      <view class="info-row">
        <text class="label">出生时间：</text>
        <picker mode="time" value="{{birthTime}}" bindchange="onTimeChange">
          <view class="picker">{{birthTime || '请选择'}}</view>
        </picker>
      </view>

      <view class="info-row">
        <text class="label">性别：</text>
        <radio-group bindchange="onGenderChange">
          <label class="radio">
            <radio value="男" checked="{{isMale}}" />男
          </label>
          <label class="radio">
            <radio value="女" checked="{{!isMale}}" />女
          </label>
        </radio-group>
      </view>
    </view>

    <!-- 开始按钮 -->
    <button
      class="start-btn"
      bindtap="onStartAnalysis"
      loading="{{isAnalyzing}}"
      disabled="{{isAnalyzing}}"
    >
      {{isAnalyzing ? '正在排盘...' : '开始排盘'}}
    </button>
  </view>

  <!-- 结果显示区域 -->
  <view class="result-section" wx:if="{{showResult}}">
    <!-- 四柱八字 -->
    <view class="bazi-chart">
      <text class="section-title">四柱八字</text>
      <view class="pillars">
        <view class="pillar">
          <text class="pillar-name">年柱</text>
          <text class="pillar-value">{{formattedBazi.year}}</text>
        </view>
        <view class="pillar">
          <text class="pillar-name">月柱</text>
          <text class="pillar-value">{{formattedBazi.month}}</text>
        </view>
        <view class="pillar">
          <text class="pillar-name">日柱</text>
          <text class="pillar-value">{{formattedBazi.day}}</text>
        </view>
        <view class="pillar">
          <text class="pillar-name">时柱</text>
          <text class="pillar-value">{{formattedBazi.hour}}</text>
        </view>
      </view>
      <view class="daymaster">
        <text>日主：{{analysis.dayMaster}}（{{analysis.dayMasterElement}}）</text>
      </view>
    </view>

    <!-- 格局分析 -->
    <view class="pattern-analysis">
      <text class="section-title">格局分析</text>
      <view class="analysis-content">
        <text class="pattern">格局：{{analysis.pattern.pattern}}</text>
        <text class="strength">强弱：{{analysis.strength.level}}</text>
        <text class="pattern-desc">{{analysis.pattern.analysis[0] || '普通格局，需要综合分析'}}</text>
      </view>
    </view>

    <!-- 十神分布 -->
    <view class="tengod-analysis">
      <text class="section-title">十神分布</text>
      <view class="tengod-content">
        <text class="tengod-text">{{formatTenGodsDistribution(analysis.tenGods.distribution)}}</text>
      </view>
    </view>

    <!-- 大运流年 -->
    <view class="luck-analysis">
      <text class="section-title">大运流年</text>
      <view class="luck-content">
        <text class="current-luck">当前大运：{{formatDayunInfo(currentDayun)}}</text>
        <text class="current-year">当前流年：{{currentLiunian.ganzhi}}年</text>
      </view>
    </view>

    <!-- 专项分析 -->
    <view class="custom-analysis">
      <text class="section-title">专项分析</text>
      <view class="analysis-content">
        <text class="question-type">问题类型：{{customAnalysis.questionType}}</text>
        <text class="analysis-result">{{formatBaziAnalysis(customAnalysis)}}</text>
      </view>
    </view>

    <!-- 重新排盘按钮 -->
    <button class="restart-btn" bindtap="onRestart">重新排盘</button>
  </view>
</view>