// pages/bazi/bazi.js - 子平八字页面
const app = getApp();

import {
  calculateBazi,
  formatBazi,
  getElementsDistribution
} from '../../utils/bazi-calculator.js';

import {
  comprehensiveBaziAnalysis
} from '../../utils/bazi-analysis.js';

import {
  calculateStartLuck,
  calculateDayun,
  getCurrentDayun,
  getCurrentLiunian,
  predictFortune
} from '../../utils/bazi-luck.js';

import {
  generateBaziAnalysis
} from '../../utils/question-analysis.js';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 输入信息
    question: '',
    birthDate: '',
    birthTime: '',
    isMale: true,

    // 八字信息
    bazi: null,
    formattedBazi: null,

    // 分析结果
    analysis: null,
    customAnalysis: null,

    // 大运流年
    dayunList: [],
    currentDayun: null,
    currentLiunian: null,

    // 界面状态
    isAnalyzing: false,
    showResult: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置默认值
    const now = new Date();
    this.setData({
      birthDate: now.toISOString().split('T')[0],
      birthTime: '12:00'
    });
  },

  // 输入问题
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    });
  },

  // 选择出生日期
  onDateChange(e) {
    this.setData({
      birthDate: e.detail.value
    });
  },

  // 选择出生时间
  onTimeChange(e) {
    this.setData({
      birthTime: e.detail.value
    });
  },

  // 选择性别
  onGenderChange(e) {
    this.setData({
      isMale: e.detail.value === '男'
    });
  },

  // 开始排盘
  onStartAnalysis() {
    if (!this.data.question.trim()) {
      wx.showToast({
        title: '请输入要问的问题',
        icon: 'none'
      });
      return;
    }

    if (!this.data.birthDate || !this.data.birthTime) {
      wx.showToast({
        title: '请选择出生日期和时间',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isAnalyzing: true,
      showResult: false
    });

    // 计算八字
    this.calculateBaziChart();
  },

  // 计算八字排盘
  calculateBaziChart() {
    try {
      // 构建出生日期时间
      const birthDateTime = new Date(`${this.data.birthDate}T${this.data.birthTime}:00`);

      // 计算四柱八字
      const bazi = calculateBazi(birthDateTime);
      const formattedBazi = formatBazi(bazi);

      // 进行八字分析
      const analysis = comprehensiveBaziAnalysis(bazi);

      // 进行精准问题分析
      const customAnalysis = generateBaziAnalysis(
        this.data.question,
        bazi,
        analysis,
        this.data.isMale
      );

      // 计算大运流年
      const currentAge = new Date().getFullYear() - birthDateTime.getFullYear();
      const startLuck = calculateStartLuck(bazi, this.data.isMale);
      const dayunList = calculateDayun(bazi, startLuck);
      const currentDayun = getCurrentDayun(dayunList, currentAge);
      const currentLiunian = getCurrentLiunian(new Date().getFullYear());

      this.setData({
        bazi: bazi,
        formattedBazi: formattedBazi,
        analysis: analysis,
        customAnalysis: customAnalysis,
        dayunList: dayunList,
        currentDayun: currentDayun,
        currentLiunian: currentLiunian,
        isAnalyzing: false,
        showResult: true
      });

      wx.showToast({
        title: '排盘完成',
        icon: 'success'
      });

    } catch (error) {
      console.error('八字计算错误:', error);
      this.setData({
        isAnalyzing: false
      });

      wx.showToast({
        title: '计算出错，请重试',
        icon: 'none'
      });
    }
  },

  // 格式化八字专项分析
  formatBaziAnalysis(customAnalysis) {
    const analysis = customAnalysis.specificAnalysis;
    let result = '';

    switch (customAnalysis.questionType) {
      case '财运':
        result = `财星分析：${analysis.wealthStar || '需要观察财星'}
投资时机：${analysis.timing || '需要综合判断'}
预期收益：${analysis.profit || '收益不明确'}
风险评估：${analysis.risk || '风险可控'}
投资建议：${analysis.advice || '谨慎理财'}`;
        break;

      case '事业':
        result = `官星分析：${analysis.officialStar || '需要观察官星'}
升职前景：${analysis.promotion || '需要努力'}
跳槽建议：${analysis.jobChange || '稳定为主'}
行动时机：${analysis.timing || '顺势而为'}
事业建议：${analysis.advice || '踏实工作'}`;
        break;

      case '婚姻':
      case '桃花':
        result = `配偶星分析：${analysis.spouseStar || '需要观察配偶星'}
感情运势：${analysis.relationship || '缘分未到'}
结婚时机：${analysis.timing || '顺其自然'}
对象特征：${analysis.partner || '合适即可'}
婚姻建议：${analysis.advice || '真诚待人'}`;
        break;

      default:
        result = `综合分析：${analysis.advice || '根据八字格局和用神喜忌综合判断'}`;
    }

    return result;
  },

  // 格式化十神分布
  formatTenGodsDistribution(distribution) {
    let result = [];
    Object.keys(distribution).forEach(god => {
      if (distribution[god].length > 0) {
        result.push(`${god}：${distribution[god].join('、')}`);
      }
    });
    return result.length > 0 ? result.join('\n') : '十神分布均匀';
  },

  // 格式化大运信息
  formatDayunInfo(dayun) {
    if (!dayun) return '暂无大运信息';
    return `${dayun.ganzhi}运（${dayun.startAge}-${dayun.endAge}岁）`;
  },

  // 重新排盘
  onRestart() {
    this.setData({
      question: '',
      bazi: null,
      formattedBazi: null,
      analysis: null,
      customAnalysis: null,
      dayunList: [],
      currentDayun: null,
      currentLiunian: null,
      isAnalyzing: false,
      showResult: false
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})