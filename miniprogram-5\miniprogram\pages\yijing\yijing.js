// pages/yijing/yijing.js - 周易卦象（六爻）页面
const app = getApp();

import {
  generateLiuyaoInfo,
  analyzeLiuyao
} from '../../utils/liuyao-data.js';

import {
  identifyQuestionType,
  generateCustomAnalysis
} from '../../utils/question-analysis.js';

Page({
  data: {
    question: '', // 要问的事
    hexagram: null, // 卦象结果
    analysis: '', // 分析结果
    isAnalyzing: false, // 是否正在分析
    coinResults: [], // 投币结果
    isThrowingCoins: false, // 是否正在投币
    currentThrow: 0, // 当前投币次数
    totalThrows: 6 // 总投币次数
  },

  onLoad() {
    console.log('周易卦象页面加载');
  },

  // 输入问题
  onQuestionInput(e) {
    this.setData({
      question: e.detail.value
    });
  },

  // 开始六爻起卦
  onStartDivination() {
    if (this.data.isThrowingCoins || this.data.isAnalyzing) return;

    // 检查是否输入问题
    if (!this.data.question.trim()) {
      wx.showToast({
        title: '请输入要问的事',
        icon: 'none'
      });
      return;
    }



    // 重置状态
    this.setData({
      coinResults: [],
      hexagram: null,
      analysis: '',
      isThrowingCoins: true,
      currentThrow: 0
    });

    // 开始投币
    this.throwCoins();
  },

  // 投币起卦（模拟三枚铜钱法）
  throwCoins() {
    if (this.data.currentThrow >= this.data.totalThrows) {
      // 投币完成，生成卦象
      this.generateHexagram();
      return;
    }

    // 模拟投币过程
    setTimeout(() => {
      // 三枚铜钱，每枚有正反两面
      // 正面为阳（3），反面为阴（2）
      // 三枚铜钱的组合：
      // 三个正面 = 9 (老阳，动爻)
      // 两正一反 = 8 (少阴，静爻)
      // 一正两反 = 7 (少阳，静爻)
      // 三个反面 = 6 (老阴，动爻)

      const coin1 = Math.random() > 0.5 ? 3 : 2; // 正面3，反面2
      const coin2 = Math.random() > 0.5 ? 3 : 2;
      const coin3 = Math.random() > 0.5 ? 3 : 2;
      const total = coin1 + coin2 + coin3;

      let yaoType, yaoSymbol, isChanging;

      switch(total) {
        case 9: // 老阳，动爻
          yaoType = '老阳';
          yaoSymbol = '━━━';
          isChanging = true;
          break;
        case 8: // 少阴，静爻
          yaoType = '少阴';
          yaoSymbol = '━ ━';
          isChanging = false;
          break;
        case 7: // 少阳，静爻
          yaoType = '少阳';
          yaoSymbol = '━━━';
          isChanging = false;
          break;
        case 6: // 老阴，动爻
          yaoType = '老阴';
          yaoSymbol = '━ ━';
          isChanging = true;
          break;
      }

      const newResults = [...this.data.coinResults];
      newResults.push({
        throw: this.data.currentThrow + 1,
        coins: [coin1, coin2, coin3],
        total: total,
        yaoType: yaoType,
        yaoSymbol: yaoSymbol,
        isChanging: isChanging
      });

      this.setData({
        coinResults: newResults,
        currentThrow: this.data.currentThrow + 1
      });

      // 继续下一次投币
      setTimeout(() => {
        this.throwCoins();
      }, 1000);
    }, 800);
  },

  // 生成卦象
  generateHexagram() {
    this.setData({
      isThrowingCoins: false,
      isAnalyzing: true
    });

    // 根据六爻结果生成本卦和变卦
    const yaos = this.data.coinResults;

    // 生成本卦（从下往上）
    const originalLines = yaos.map(yao => {
      return yao.yaoType === '老阳' || yao.yaoType === '少阳' ? 1 : 0; // 1为阳，0为阴
    });

    // 生成变卦
    const changedLines = yaos.map(yao => {
      if (yao.isChanging) {
        // 动爻变化：老阳变少阴，老阴变少阳
        return yao.yaoType === '老阳' ? 0 : 1;
      } else {
        // 静爻不变
        return yao.yaoType === '少阳' ? 1 : 0;
      }
    });

    // 获取动爻位置
    const changingYaos = yaos.map((yao, index) => yao.isChanging ? index + 1 : null).filter(x => x !== null);

    // 生成六爻装卦信息
    const liuyaoInfo = generateLiuyaoInfo(originalLines, this.data.question, new Date());

    const hexagram = {
      question: this.data.question,
      originalLines: originalLines,
      changedLines: changedLines,
      changingYaos: changingYaos,
      yaos: yaos,
      liuyaoInfo: liuyaoInfo,
      time: new Date().toLocaleString('zh-CN')
    };

    setTimeout(() => {
      this.setData({
        hexagram: hexagram
      });

      // AI分析
      this.analyzeHexagram(hexagram);
    }, 1500);
  },

  // 分析卦象
  analyzeHexagram(hexagram) {
    setTimeout(() => {
      // 进行六爻专业分析
      const liuyaoAnalysis = analyzeLiuyao(hexagram.liuyaoInfo, hexagram.changingYaos, hexagram.question);

      // 进行精准问题分析
      const customAnalysis = generateCustomAnalysis(
        hexagram.question,
        {
          upper: { number: hexagram.liuyaoInfo.upperTrigram },
          lower: { number: hexagram.liuyaoInfo.lowerTrigram }
        },
        { result: '平', description: '六爻分析' }, // 简化的体用分析
        null,
        null
      );

      const changingText = hexagram.changingYaos.length > 0 ?
        `动爻：第${hexagram.changingYaos.join('、')}爻` : '无动爻';

      const analysis = `【周易六爻占卜结果】

所问之事：${hexagram.question}
起卦时间：${hexagram.time}
起卦方法：三枚铜钱法

【卦象信息】
卦名：${liuyaoAnalysis.hexagramName}
${changingText}

【装卦详解】
${hexagram.yaos.map((yao, index) => {
  const yaoNum = index + 1;
  const branch = hexagram.liuyaoInfo.branches[index];
  const relative = hexagram.liuyaoInfo.relatives[index];
  const spirit = hexagram.liuyaoInfo.spirits[index];
  const isWorld = liuyaoAnalysis.worldResponse.world === yaoNum;
  const isResponse = liuyaoAnalysis.worldResponse.response === yaoNum;
  const worldResponseText = isWorld ? '（世）' : isResponse ? '（应）' : '';

  return `第${yaoNum}爻：${yao.yaoSymbol} ${branch}${relative} ${spirit}${worldResponseText}${yao.isChanging ? ' 动' : ''}`;
}).reverse().join('\n')}

【专项分析 - ${customAnalysis.questionType}】
用神：${liuyaoAnalysis.useGod}
${this.formatLiuyaoAnalysis(customAnalysis)}

【六爻要点】
${liuyaoAnalysis.keyPoints.join('\n')}

【综合判断】
${this.getLiuyaoAdvice(liuyaoAnalysis, customAnalysis)}

【古籍依据】
根据传统六爻理论，世应关系、用神旺衰、动静生克为断卦要诀。
此卦${liuyaoAnalysis.hexagramName}，${liuyaoAnalysis.changingYaos.length > 0 ? '有动爻主变化' : '静卦主稳定'}。

注：此为传统六爻占法，仅供参考，最终决策请结合实际情况。`;

      this.setData({
        analysis: analysis,
        isAnalyzing: false
      });

      wx.showToast({
        title: '占卜完成',
        icon: 'success'
      });
    }, 2000);
  },

  // 格式化六爻分析结果
  formatLiuyaoAnalysis(customAnalysis) {
    const analysis = customAnalysis.specificAnalysis;
    let result = '';

    switch (customAnalysis.questionType) {
      case '财运':
        result = `财运分析：${analysis.timing || '需要观察用神旺衰'}
投资建议：${analysis.advice || '以用神生克为准'}`;
        break;

      case '学业':
        result = `学业运势：${analysis.examResult || '看父母爻旺衰'}
学习建议：${analysis.advice || '父母爻旺则利学业'}`;
        break;

      case '事业':
        result = `事业前景：${analysis.promotion || '看官鬼爻旺衰'}
工作建议：${analysis.advice || '官鬼爻旺则利事业'}`;
        break;

      case '婚姻':
        result = `感情运势：${analysis.relationship || '看用神旺衰'}
婚姻建议：${analysis.advice || '用神旺则感情顺利'}`;
        break;

      default:
        result = `综合分析：根据用神旺衰和世应关系综合判断`;
    }

    return result;
  },

  // 获取六爻综合建议
  getLiuyaoAdvice(liuyaoAnalysis, customAnalysis) {
    let advice = '';

    // 根据动爻情况判断
    if (liuyaoAnalysis.changingYaos.length === 0) {
      advice = '卦无动爻，事情发展缓慢，宜静待时机，不宜急进。';
    } else if (liuyaoAnalysis.changingYaos.length === 1) {
      advice = '一爻独动，变化明确，可根据动爻性质判断吉凶。';
    } else if (liuyaoAnalysis.changingYaos.length >= 3) {
      advice = '多爻齐动，变化复杂，事情多变，需要谨慎应对。';
    } else {
      advice = '二爻发动，有一定变化，需要综合分析动爻关系。';
    }

    // 根据用神情况补充建议
    if (liuyaoAnalysis.useGod && liuyaoAnalysis.keyPoints.some(point => point.includes('用神不现'))) {
      advice += '用神不现，所求之事难以如愿，建议另寻他法或等待时机。';
    } else {
      advice += '用神在卦，所求之事有望，需要观察用神旺衰和生克关系。';
    }

    return advice;
  },

  // 重新起卦
  onRestart() {
    this.setData({
      hexagram: null,
      analysis: '',
      coinResults: [],
      isAnalyzing: false,
      isThrowingCoins: false,
      currentThrow: 0
    });
  }
});