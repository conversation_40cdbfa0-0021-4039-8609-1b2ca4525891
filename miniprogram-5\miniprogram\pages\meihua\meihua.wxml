<!--pages/meihua/meihua.wxml - 梅花易数页面模板-->
<view class="meihua-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-title">梅花易数</view>
    <view class="header-subtitle">邵雍原著 • 高阶占卜</view>
  </view>

  <!-- 问事输入 -->
  <view class="question-section" wx:if="{{!hexagram && !isAnalyzing}}">
    <view class="section-title">要问的事</view>
    <ink-input
      label=""
      placeholder="请输入您要占卜的问题"
      value="{{question}}"
      maxlength="100"
      bind:input="onQuestionInput">
    </ink-input>
  </view>

  <!-- 起卦方法选择 -->
  <view class="method-section" wx:if="{{!hexagram && !isAnalyzing}}">
    <view class="section-title">起卦方法</view>
    <picker range="{{methodList}}" range-key="name" bindchange="onMethodChange">
      <view class="method-picker">
        <text class="method-name" wx:for="{{methodList}}" wx:key="id" wx:if="{{item.id === selectedMethod}}">{{item.name}}</text>
        <text class="method-desc" wx:for="{{methodList}}" wx:key="id" wx:if="{{item.id === selectedMethod}}">{{item.desc}}</text>
        <text class="picker-arrow">▼</text>
      </view>
    </picker>
  </view>

  <!-- 当前时间显示（时间起卦时） -->
  <view class="time-section" wx:if="{{!hexagram && !isAnalyzing && selectedMethod === 'time'}}">
    <view class="time-label">当前时间</view>
    <view class="time-display">{{currentTime}}</view>
    <view class="time-tip">根据当前年月日时进行起卦</view>
  </view>

  <!-- 数字输入（物数、声音、丈尺、尺寸起卦） -->
  <view class="input-section" wx:if="{{!hexagram && !isAnalyzing && (selectedMethod === 'number' || selectedMethod === 'sound' || selectedMethod === 'measure' || selectedMethod === 'chisun')}}">
    <view class="section-title">
      {{selectedMethod === 'number' ? '输入数字' : selectedMethod === 'sound' ? '声音次数' : selectedMethod === 'measure' ? '丈尺数' : '尺寸数'}}
    </view>
    <ink-input
      label=""
      type="number"
      placeholder="{{selectedMethod === 'number' ? '请输入看到的数字' : selectedMethod === 'sound' ? '请输入听到的声音次数' : selectedMethod === 'measure' ? '请输入丈尺数（如：3丈5尺输入35）' : '请输入尺寸数（如：2尺3寸输入23）'}}"
      value="{{inputNumber}}"
      bind:input="onNumberInput">
    </ink-input>
    <view class="input-tip">
      {{selectedMethod === 'number' ? '根据您看到的任意数字起卦' : selectedMethod === 'sound' ? '根据您听到的声音次数起卦' : selectedMethod === 'measure' ? '以丈数为上卦，尺数为下卦' : '以尺数为上卦，寸数为下卦'}}
    </view>
  </view>

  <!-- 文字输入（字数、为人、自己、动物、静物、形物、验色起卦） -->
  <view class="input-section" wx:if="{{!hexagram && !isAnalyzing && (selectedMethod === 'word' || selectedMethod === 'person' || selectedMethod === 'self' || selectedMethod === 'animal' || selectedMethod === 'static' || selectedMethod === 'shape' || selectedMethod === 'color')}}">
    <view class="section-title">
      {{selectedMethod === 'word' ? '输入文字' : selectedMethod === 'person' ? '观察特征' : selectedMethod === 'self' ? '观察现象' : selectedMethod === 'animal' ? '动物方位' : selectedMethod === 'static' ? '静物名称' : selectedMethod === 'shape' ? '物体形状' : '颜色'}}
    </view>
    <ink-input
      label=""
      placeholder="{{selectedMethod === 'word' ? '请输入要起卦的文字' : selectedMethod === 'person' ? '请输入观察到的人物特征' : selectedMethod === 'self' ? '请输入观察到的现象' : selectedMethod === 'animal' ? '请输入动物和方位信息' : selectedMethod === 'static' ? '请输入静物名称（如：屋宅、树木、器物）' : selectedMethod === 'shape' ? '请输入物体形状（如：圆、方、长、短）' : '请输入颜色（如：红、黄、蓝、绿）'}}"
      value="{{inputWords}}"
      maxlength="50"
      bind:input="onWordsInput">
    </ink-input>
    <view class="input-tip">
      {{selectedMethod === 'word' ? '根据文字的字数进行起卦' : selectedMethod === 'person' ? '听语声、观人品、取诸身物' : selectedMethod === 'self' ? '年月日时或闻声观物' : selectedMethod === 'animal' ? '以物为上卦，方位为下卦' : selectedMethod === 'static' ? '屋宅、树木、器物等静物起卦' : selectedMethod === 'shape' ? '根据物体形状特征起卦' : '根据颜色对应八卦起卦'}}
    </view>
  </view>

  <!-- 起卦按钮 -->
  <view class="action-section" wx:if="{{!hexagram && !isAnalyzing}}">
    <ink-button
      text="开始占卜"
      type="primary"
      size="large"
      bind:tap="onStartDivination">
    </ink-button>

  </view>

  <!-- 分析中状态 -->
  <view class="analyzing-section" wx:if="{{isAnalyzing}}">
    <view class="analyzing-animation">
      <view class="ink-drop"></view>
      <view class="ink-drop"></view>
      <view class="ink-drop"></view>
    </view>
    <view class="analyzing-text">正在起卦分析中...</view>
  </view>

  <!-- 卦象结果 -->
  <view class="hexagram-section" wx:if="{{hexagram}}">
    <view class="hexagram-header">
      <view class="hexagram-title">卦象结果</view>
      <view class="hexagram-question">所问：{{hexagram.question}}</view>
      <view class="hexagram-time">起卦时间：{{hexagram.time}}</view>
    </view>

    <view class="hexagram-display">
      <view class="trigram-container">
        <view class="trigram upper-trigram">
          <view class="trigram-symbol">{{hexagram.upper.symbol}}</view>
          <view class="trigram-name">{{hexagram.upper.name}}卦</view>
          <view class="trigram-label">上卦 ({{hexagram.upper.number}}数)</view>
        </view>

        <view class="hexagram-center">
          <view class="change-line">变爻：第{{hexagram.change}}爻</view>
        </view>

        <view class="trigram lower-trigram">
          <view class="trigram-symbol">{{hexagram.lower.symbol}}</view>
          <view class="trigram-name">{{hexagram.lower.name}}卦</view>
          <view class="trigram-label">下卦 ({{hexagram.lower.number}}数)</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 分析结果 -->
  <view class="analysis-section" wx:if="{{analysis}}">
    <view class="analysis-header">
      <view class="analysis-title">AI智能解析</view>
    </view>
    
    <view class="analysis-content">
      <text class="analysis-text">{{analysis}}</text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="bottom-actions" wx:if="{{hexagram}}">
    <ink-button
      text="重新占卜"
      type="secondary"
      size="large"
      bind:tap="onRestart">
    </ink-button>
  </view>
</view>
