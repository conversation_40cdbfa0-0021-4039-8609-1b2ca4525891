<!--pages/index/index.wxml - 主界面模板-->
<view class="index-container">
  <!-- 顶部用户信息区域 -->
  <view class="user-header ink-gradient-bg">
    <view class="user-info">
      <view class="user-avatar" wx:if="{{isLoggedIn}}">
        <image src="{{userInfo.avatarUrl}}" class="avatar-img"></image>
      </view>
      <view class="user-avatar guest-avatar" wx:else>
        <text class="guest-icon">客</text>
      </view>

      <view class="user-details">
        <view class="user-name">
          <text wx:if="{{isLoggedIn}}">用户：{{userInfo.nickName}}</text>
          <text wx:else>游客模式</text>
        </view>
      </view>
    </view>

    <!-- 登录提示 -->
    <view class="login-tip" wx:if="{{!isLoggedIn}}">
      <view class="login-button ink-ripple" bindtap="showLoginTip">
        <text class="login-text">点击登录</text>
      </view>
    </view>
  </view>

  <!-- 主标题区域 -->
  <view class="main-title">
    <view class="title-text ink-title">元亨利贞 • 古籍智慧传承</view>
    <view class="title-decoration ink-divider"></view>
  </view>

  <!-- 功能模块列表 -->
  <view class="function-list">
    <view
      class="function-item ink-fade-in ink-ripple"
      wx:for="{{functionList}}"
      wx:key="id"
      data-id="{{item.id}}"

      data-route="{{item.route}}"
      bindtap="onClickFunction"
    >
      <view class="item-background" style="background: {{item.color}};"></view>
      <view class="item-content">
        <!-- 左侧图标区域 -->
        <view class="item-icon-section">
          <view class="item-icon">
            <text class="icon-text">{{item.icon}}</text>
          </view>
        </view>

        <!-- 中间信息区域 -->
        <view class="item-info-section">
          <view class="item-title">{{item.title}}</view>
          <view class="item-subtitle">{{item.subtitle}}</view>

        </view>

        <!-- 右侧描述区域 -->
        <view class="item-description-section">
          <text class="description-text">{{item.descriptionLine1}}</text>
          <text class="description-text">{{item.descriptionLine2}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 免费功能区域 -->
  <view class="free-section">
    <view class="free-grid">
      <view
        class="free-card ink-fade-in ink-ripple"
        wx:for="{{freeFeatures}}"
        wx:key="id"
        data-route="{{item.route}}"
        bindtap="onClickFreeFeature"
      >
        <view class="free-card-content">
          <view class="free-icon">
            <text class="free-icon-text">{{item.icon}}</text>
          </view>
          <view class="free-info">
            <view class="free-title">{{item.title}}</view>
            <view class="free-subtitle">{{item.subtitle}}</view>
          </view>
        </view>
        <view class="free-description">
          <text class="free-desc-text">{{item.description}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 购物车入口 -->
  <view class="shopping-section">
    <view class="shopping-card ink-ripple" bindtap="onGoShopping">
      <view class="shopping-content">
        <view class="shopping-icon">🛒</view>
        <view class="shopping-info">
          <view class="shopping-title">购物车</view>
          <view class="shopping-subtitle">定制推荐</view>
        </view>
        <view class="shopping-arrow">→</view>
      </view>
    </view>
  </view>


</view>