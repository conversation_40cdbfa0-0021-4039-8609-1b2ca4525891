// 问题分类和精准分析系统
// 基于传统六爻理论和梅花易数知识库

// 问题类型识别关键词
export const QUESTION_KEYWORDS = {
  财运: ['财运', '财富', '赚钱', '收入', '投资', '理财', '股票', '基金', '生意', '买卖', '经商', '盈利', '亏损', '金钱'],
  学业: ['学业', '考试', '升学', '读书', '学习', '成绩', '文凭', '学校', '教育', '培训', '进修'],
  事业: ['工作', '事业', '职业', '升职', '跳槽', '求职', '面试', '职场', '上班', '创业', '开公司'],
  婚姻: ['婚姻', '结婚', '恋爱', '感情', '男友', '女友', '老公', '老婆', '配偶', '另一半', '对象'],
  健康: ['健康', '疾病', '生病', '身体', '医院', '治疗', '康复', '病情', '手术'],
  子女: ['子女', '孩子', '生育', '怀孕', '生子', '儿子', '女儿', '小孩', '宝宝'],
  合伙: ['合伙', '合作', '伙伴', '朋友', '同事', '团队', '协作'],
  桃花: ['桃花', '异性', '追求', '表白', '相亲', '脱单', '恋爱运']
};

// 六亲对应关系
export const LIUQIN_MAPPING = {
  财运: '妻财',
  学业: '父母',
  事业: '官鬼',
  子女: '子孙',
  合伙: '兄弟',
  健康: '相应六亲', // 根据具体疾病看相应六亲
  婚姻: { 男: '妻财', 女: '官鬼' },
  桃花: { 男: '妻财', 女: '官鬼' }
};

// 识别问题类型
export function identifyQuestionType(question) {
  const questionLower = question.toLowerCase();
  
  for (const [type, keywords] of Object.entries(QUESTION_KEYWORDS)) {
    for (const keyword of keywords) {
      if (questionLower.includes(keyword)) {
        return type;
      }
    }
  }
  
  return '综合'; // 默认类型
}

// 财运专项分析
export function analyzeWealth(hexagram, bodyUseAnalysis, questionType) {
  const analysis = {
    focus: '妻财爻',
    timing: '',
    profit: '',
    risk: '',
    advice: ''
  };
  
  // 基于体用生克分析财运
  if (bodyUseAnalysis.result === '吉') {
    if (bodyUseAnalysis.relationship === 'generate') {
      analysis.timing = '当前时机有利，宜在1-3个月内行动';
      analysis.profit = '预期收益较好，可获利20-50%';
      analysis.advice = '用生体，财源广进，适合投资。建议选择稳健型投资项目。';
    }
  } else if (bodyUseAnalysis.result === '凶') {
    if (bodyUseAnalysis.relationship === 'overcome') {
      analysis.timing = '当前不宜投资，建议等待6个月后';
      analysis.risk = '投资风险极高，可能亏损30-70%';
      analysis.advice = '用克体，财运受阻，强行投资必有损失。建议保守理财，等待时机。';
    }
  }
  
  // 根据卦象五行属性细化分析
  const upperElement = getTrigramElement(hexagram.upper.number);
  const lowerElement = getTrigramElement(hexagram.lower.number);
  
  if (upperElement === '金' || lowerElement === '金') {
    analysis.advice += '金主财，可关注金融、贵金属投资。';
  } else if (upperElement === '水' || lowerElement === '水') {
    analysis.advice += '水主流动，适合短期投资、流动性强的项目。';
  }
  
  return analysis;
}

// 学业专项分析
export function analyzeEducation(hexagram, bodyUseAnalysis) {
  const analysis = {
    focus: '父母爻',
    examResult: '',
    studyDirection: '',
    timing: '',
    advice: ''
  };
  
  if (bodyUseAnalysis.result === '吉') {
    analysis.examResult = '考试运势良好，有望取得理想成绩';
    analysis.timing = '适合在近期报考或参加重要考试';
    analysis.studyDirection = '宜选择文科或管理类专业';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.examResult = '考试阻力较大，需要加倍努力';
    analysis.timing = '建议推迟考试时间，充分准备后再战';
    analysis.studyDirection = '可考虑技能型或实用性专业';
  }
  
  // 根据卦象特性分析学习方向
  const upperAttrs = getTrigramAttributes(hexagram.upper.number);
  if (upperAttrs.nature.includes('文')) {
    analysis.studyDirection += '，文学、教育类专业有利';
  }
  
  return analysis;
}

// 事业专项分析
export function analyzeCareer(hexagram, bodyUseAnalysis) {
  const analysis = {
    focus: '官鬼爻',
    promotion: '',
    jobChange: '',
    timing: '',
    advice: ''
  };
  
  if (bodyUseAnalysis.result === '吉') {
    analysis.promotion = '升职机会较好，有贵人相助';
    analysis.jobChange = '适合主动寻求更好的工作机会';
    analysis.timing = '宜在2-4个月内采取行动';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.promotion = '升职阻力较大，需要耐心等待';
    analysis.jobChange = '不宜贸然跳槽，容易遇到困难';
    analysis.timing = '建议等待半年后再做决定';
  }
  
  return analysis;
}

// 婚姻专项分析（需要性别信息）
export function analyzeMarriage(hexagram, bodyUseAnalysis, gender = '男') {
  const analysis = {
    focus: gender === '男' ? '妻财爻' : '官鬼爻',
    relationship: '',
    timing: '',
    partner: '',
    advice: ''
  };
  
  if (bodyUseAnalysis.result === '吉') {
    analysis.relationship = '感情运势良好，有望遇到合适对象';
    analysis.timing = '适合在春夏季节主动寻找感情';
    analysis.partner = '对象可能是温和、有才华的人';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.relationship = '感情路上多波折，需要耐心';
    analysis.timing = '不宜急于求成，建议等待合适时机';
    analysis.partner = '要谨慎选择，避免不合适的对象';
  }
  
  return analysis;
}

// 健康专项分析
export function analyzeHealth(hexagram, bodyUseAnalysis, question = '') {
  // 根据具体疾病类型确定相应六亲
  let focusLiuqin = '相应六亲';
  let specificFocus = '';

  const questionLower = question.toLowerCase();

  // 根据疾病类型确定六亲
  if (questionLower.includes('心脏') || questionLower.includes('血液') || questionLower.includes('精神')) {
    focusLiuqin = '官鬼爻';
    specificFocus = '心脏、血液、精神类疾病看官鬼爻';
  } else if (questionLower.includes('肠胃') || questionLower.includes('消化') || questionLower.includes('脾胃')) {
    focusLiuqin = '妻财爻';
    specificFocus = '肠胃、消化系统疾病看妻财爻';
  } else if (questionLower.includes('肺') || questionLower.includes('呼吸') || questionLower.includes('皮肤')) {
    focusLiuqin = '父母爻';
    specificFocus = '肺部、呼吸系统、皮肤疾病看父母爻';
  } else if (questionLower.includes('肝') || questionLower.includes('筋骨') || questionLower.includes('四肢')) {
    focusLiuqin = '兄弟爻';
    specificFocus = '肝脏、筋骨、四肢疾病看兄弟爻';
  } else if (questionLower.includes('肾') || questionLower.includes('生殖') || questionLower.includes('泌尿')) {
    focusLiuqin = '子孙爻';
    specificFocus = '肾脏、生殖、泌尿系统疾病看子孙爻';
  } else {
    // 一般疾病看官鬼爻（病神）
    focusLiuqin = '官鬼爻';
    specificFocus = '一般疾病看官鬼爻（病神）';
  }

  const analysis = {
    focus: focusLiuqin,
    specificFocus: specificFocus,
    condition: '',
    recovery: '',
    prevention: '',
    advice: ''
  };

  if (bodyUseAnalysis.result === '吉') {
    analysis.condition = '身体状况良好，疾病有望康复';
    analysis.recovery = '恢复期较短，约1-2个月';
    analysis.prevention = '注意日常保养，可预防疾病';
    analysis.advice = '体用相生，身体康复有望，宜配合治疗，注意调养。';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.condition = '需要重视健康问题，及时就医';
    analysis.recovery = '恢复期较长，需要耐心治疗';
    analysis.prevention = '要特别注意预防，避免病情加重';
    analysis.advice = '体用相克，病情较重，必须及时就医，不可拖延。';
  } else {
    analysis.condition = '身体状况平稳，需要持续关注';
    analysis.recovery = '恢复期中等，约2-3个月';
    analysis.prevention = '保持良好生活习惯，定期检查';
    analysis.advice = '体用平和，病情稳定，宜静养调理，循序渐进。';
  }

  return analysis;
}

// 合伙专项分析
export function analyzePartnership(hexagram, bodyUseAnalysis) {
  const analysis = {
    focus: '兄弟爻',
    cooperation: '',
    profit: '',
    risk: '',
    advice: ''
  };
  
  if (bodyUseAnalysis.result === '吉') {
    analysis.cooperation = '合作关系和谐，有利共同发展';
    analysis.profit = '合伙有利，可实现共赢';
    analysis.risk = '风险较小，但需要明确分工';
  } else if (bodyUseAnalysis.result === '凶') {
    analysis.cooperation = '合作中容易产生分歧和矛盾';
    analysis.profit = '利益分配可能不均，需要谨慎';
    analysis.risk = '合伙风险较大，建议重新考虑';
  }
  
  return analysis;
}

// 获取八卦五行属性
function getTrigramElement(trigramNumber) {
  const elementMap = {
    1: '金', 2: '金', // 乾、兑
    3: '火',          // 离
    4: '木', 5: '木', // 震、巽
    6: '水',          // 坎
    7: '土', 8: '土'  // 艮、坤
  };
  return elementMap[trigramNumber] || '土';
}

// 获取八卦万物属类（简化版）
function getTrigramAttributes(trigramNumber) {
  // 这里应该引用完整的TRIGRAM_ATTRIBUTES，简化处理
  const simpleAttrs = {
    1: { nature: ['天', '父', '官贵'] },
    2: { nature: ['泽', '少女', '口舌'] },
    3: { nature: ['火', '中女', '文书'] },
    4: { nature: ['雷', '长男', '动'] },
    5: { nature: ['风', '长女', '文'] },
    6: { nature: ['水', '中男', '险'] },
    7: { nature: ['山', '少男', '止'] },
    8: { nature: ['地', '母', '众'] }
  };
  return simpleAttrs[trigramNumber] || { nature: ['未知'] };
}

// 综合分析生成器
export function generateCustomAnalysis(question, hexagram, bodyUseAnalysis, mutualHexagram, changedHexagram) {
  const questionType = identifyQuestionType(question);
  let specificAnalysis = {};
  
  switch (questionType) {
    case '财运':
      specificAnalysis = analyzeWealth(hexagram, bodyUseAnalysis, questionType);
      break;
    case '学业':
      specificAnalysis = analyzeEducation(hexagram, bodyUseAnalysis);
      break;
    case '事业':
      specificAnalysis = analyzeCareer(hexagram, bodyUseAnalysis);
      break;
    case '婚姻':
    case '桃花':
      specificAnalysis = analyzeMarriage(hexagram, bodyUseAnalysis);
      break;
    case '健康':
      specificAnalysis = analyzeHealth(hexagram, bodyUseAnalysis, question);
      break;
    case '合伙':
      specificAnalysis = analyzePartnership(hexagram, bodyUseAnalysis);
      break;
    default:
      specificAnalysis = { focus: '综合分析', advice: '需要综合考虑各方面因素' };
  }
  
  return {
    questionType,
    specificAnalysis,
    liuqin: LIUQIN_MAPPING[questionType] || '综合'
  };
}
